export class Tile {
    count: number;
    element: HTMLDivElement;

    constructor(initialCount: number, type: number) {
        const mappedType = ((type + 1) % 5) + 1;
        const className = 'tile tile' + mappedType.toString();

        this.count = initialCount;
        this.element = document.createElement('div');
        this.element.className = className;
        this.element.innerText = this.count.toString();
    }

    setCount(newCount: number) {
        this.count = newCount;
        this.element.innerText = this.count.toString();
    }
}
