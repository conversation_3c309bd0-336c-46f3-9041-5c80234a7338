import { TilePlacement } from './TilePlacement';
import { Tile } from './Tile';
import { PlayerBoard } from './PlayerBoard';
import { Bag } from './Bag';
import { I18nTranslations, I18nObject } from './I18nTranslations';
import { waitForDomStable } from './utils';
import { BGA } from './BGA';

declare const browser: {
    runtime: {
        getURL(path: string): string;
    };
};

export class Game {
    private static readonly factoriesByPlayers: { [key: number]: number } = {
        2: 5,
        3: 7,
        4: 9
    };

    private logChangeQueue: Element[] = [];
    private isProcessingInitialLog = false;

    availableTiles: Tile[] = [];
    discardedTiles: Tile[] = [];
    stateLoaded = false;
    logsError = false;
    tilesInBagCount: number;
    boardBag: Bag;
    discardedBag: Bag;
    playersBoards: { [playerName: string]: PlayerBoard } = {};
    lastMoves: {
        [playerName: string]: {
            lineNumber: number,
            tilePlacement: TilePlacement,
            discardedTiles?: TilePlacement
        }
    } = {};
    factoriesCount: number;

    isSupported(): boolean {
        const ogUrl = document.querySelector('meta[property="og:url"]');

        return !ogUrl || !ogUrl.getAttribute('content').includes('/azul/');
    }

    init() {
        this.initializeAddOnStructure();

        // Wait until both containers exist, then wait for stability
        const checkContainers = () => {
            const logs = document.getElementById('logs');
            const factories = document.getElementById('factories');
            if (logs && factories) {
                this.initializeTranslations();
                this.initializeGame();
                this.initializeLogObserve();
                this.waitForGameStable();
            } else {
                setTimeout(checkContainers, 100);
            }
        };
        checkContainers();
    }

    private async initializeTranslations(): Promise<void> {
        const i18n = await this.getI18n();

        for (const key in I18nTranslations.operationTypeByKey) {
            if (typeof i18n.nlsStrings[i18n.activeBundle][key] !== 'undefined') {
                I18nTranslations.addTranslation(key, i18n.nlsStrings[i18n.activeBundle][key]);
            } else if (typeof i18n.nlsStrings['lang_mainsite-' + i18n.jsbundlesversion][key] !== 'undefined') {
                I18nTranslations.addTranslation(key, i18n.nlsStrings['lang_mainsite-' + i18n.jsbundlesversion][key]);
            }
        }
    }

    // https://pl.boardgamearena.com/10/azul?table=707354797
    private async loadLog(fromFile: boolean = false): Promise<void> {
        this.logsError = false;
        let data: string;
        try {
            if (fromFile === false) {
                // wait for bgaConfig to be available and get the request token
                const requestToken = await this.getBgaRequestToken();

                // Get the current page URL to extract table ID and other parameters
                const currentUrl = new URL(window.location.href);
                const tableId = currentUrl.searchParams.get('table');
                if (!tableId) {
                    console.warn('No table ID found in URL');
                    return;
                }

                // extract server ID from meta property
                const ogUrlMeta = document.querySelector('meta[property="og:url"]');
                let serverId = 0;

                if (ogUrlMeta) {
                    const ogUrl = ogUrlMeta.getAttribute('content');
                    if (ogUrl) {
                        const match = ogUrl.match(/boardgamearena\.com\/(\d+)\//);
                        if (match && match[1]) {
                            serverId = parseInt(match[1], 10);
                        }
                    }
                }

                if (serverId === 0) {
                    console.warn('No server ID found in URL');
                    return;
                }

                // extract headers from the current page's requests
                const headers = await this.getRequestHeaders(requestToken);

                const timestamp = Date.now();
                const lang = I18nTranslations.getLanguage();
                const notificationUrl = `https://${lang}.boardgamearena.com/${serverId}/azul/azul/notificationHistory.html?table=${tableId}&from=1&privateinc=1&history=0&noerrortracking=true&dojo.preventCache=${timestamp}`;


                /*const notificationUrl = `https://boardgamearena.com/table/table/notificationHistory.html?table=${tableId}&from=1&privateinc=1&history=0&noerrortracking=true&dojo.preventCache=${timestamp}`;*/

                console.log(notificationUrl);
                console.log(headers);
                const response = await fetch(notificationUrl, {
                    method: 'GET',
                    headers: headers,
                    credentials: 'include' // Include cookies automatically
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                data = await response.text();
            } else {
                const jsonUrl = browser.runtime.getURL('2_players.json');
                data = await fetch(jsonUrl).then(res => res.text());
            }

            /*const delay = millis => new Promise((resolve, reject) => {
                setTimeout(_ => resolve(), millis)
            });

            await delay(10000);*/


            console.log('Notification history loaded:', data);
            alert('gggg');
            // Parse and process the notification history data
            this.processNotificationHistory(data);

        } catch (error) {
            console.error('Error loading log:', error);
            this.logsError = true;
        }
    }

    private async getRequestHeaders(requestToken: string): Promise<Headers> {
        const headers = new Headers();

        // Set basic headers
        headers.set('User-Agent', 'Mozilla/5.0 (X11; Ubuntu; Linux x86_64; rv:131.0) Gecko/20100101 Firefox/131.0');
        headers.set('Accept', '*/*');
        headers.set('Accept-Language', 'en-US,en;q=0.5');
        headers.set('Accept-Encoding', 'gzip, deflate, br, zstd');
        headers.set('X-Requested-With', 'XMLHttpRequest');
        headers.set('Connection', 'keep-alive');
        headers.set('Referer', window.location.href);
        headers.set('Sec-Fetch-Dest', 'empty');
        headers.set('Sec-Fetch-Mode', 'cors');
        headers.set('Sec-Fetch-Site', 'same-origin');
        headers.set('TE', 'trailers');

        // Set the X-Request-Token header
        alert(requestToken);
        headers.set('X-Request-Token', requestToken);

        return headers;
    }



    private async getI18n(): Promise<I18nObject> {
        return BGA.waitForAndGetGlobalObject<I18nObject>('g_i18n');
    }

    private async getBgaRequestToken(): Promise<string> {
        return BGA.waitForAndGetGlobalObject<string>('bgaConfig.requestToken');
    }

    private processNotificationHistory(data: string): void {
        const doc = JSON.parse(data);

        console.log(doc.data.data);
        let i = 0;
        for (const packet of doc.data.data) {
            i++;
            console.log(packet);

            for (const packetEvent of packet.data) {
                console.log(packetEvent);

                if (packetEvent.type === 'factoriesFilled') {
                    console.log('DEBUG: factories', packetEvent.args.factories);
                    this.factoriesCount = packetEvent.args.factories.length - 1;

                    const totalDiscarded = this.newRoundBegins();

                    this.tilesInBagCount = packetEvent.args.remainingTiles;

                    for (const factory of packetEvent.args.factories) {
                        for (const factoryTile of factory) {
                            if (factoryTile.type === 0) {
                                continue;
                            }
                            this.reduceAvailableTiles(new TilePlacement(1, factoryTile.type));
                        }
                    }

                    console.log('DEBUG: available tiles', this.availableTiles);


                } else if (packetEvent.type === 'undoSelectLine') {
                    this.undoSelectLine(packetEvent.args.player_name);

                    console.log('DEBUG: players boards', this.playersBoards);


                } else if (packetEvent.type === 'tilesPlacedOnLine') {
                    this.placeTileOnLine(packetEvent.args.player_name, packetEvent.args.lineNumber, packetEvent.args.number, packetEvent.args.type);

                    console.log('DEBUG: players boards', this.playersBoards);
                    console.log('DEBUG: discarded bag ', this.discardedBag)

                } else if (packetEvent.type === 'tilesSelected') {
                    console.log('DEBUG: tiles selected from factory ' + packetEvent.args.fromFactory + ' color ' + packetEvent.args.color);
                }

            }
        }

    }

    private initializeGame(): void {
        this.discardedBag = new Bag();
        this.boardBag = new Bag();
        this.availableTiles = [];
        this.discardedTiles = [];

        for (let i = 1; i <= 5; i++) {
            this.availableTiles.push(new Tile(20, i));
            this.discardedTiles.push(new Tile(0, i));
        }
        this.tilesInBagCount = 100;

        const tileContainer = document.getElementById('azul_tile_counter');
        for (const tile of this.availableTiles) {
            tileContainer.appendChild(tile.element);
        }

        const discardedTileContainer = document.getElementById('azul_discarded_counter');
        for (const tile of this.discardedTiles) {
            discardedTileContainer.appendChild(tile.element);
        }

        const playersContainer = document.getElementById('player_boards');
        if (playersContainer) {
            const playerBoards = playersContainer.querySelectorAll('.player-board');
            playerBoards.forEach(board => {
                const playerNameElem = board.querySelector('.player-name a');
                if (playerNameElem) {
                    const playerName = playerNameElem.textContent.trim();
                    this.playersBoards[playerName] = new PlayerBoard();
                }
            });
        }

        // Set the number of factories based on the number of player boards
        const numPlayers = Object.keys(this.playersBoards).length;
        this.factoriesCount = Game.factoriesByPlayers[numPlayers] ?? 0;

        this.tilesInBagCount -= this.factoriesCount * 4;

        console.log(this.playersBoards);
    }

    /**
     * Helper to extract tile placements from a container.
     * Groups tiles by type and returns an array of TilePlacement.
     */
    private extractTilePlacements(container: Element): TilePlacement[] {
        console.log(container);
        const tileDivs = container.querySelectorAll('div.tile');
        console.log(tileDivs);
        const typeCountMap = new Map<number, number>();

        tileDivs.forEach(tileDiv => {
            const match = Array.from(tileDiv.classList).find(cls => cls.startsWith('tile') && cls.length > 4);
            if (match) {
                const tileType = parseInt(match.replace('tile', ''), 10);
                // first player tile
                if (tileType === 0) {
                    return;
                }
                typeCountMap.set(tileType, (typeCountMap.get(tileType) || 0) + 1);
            }
        });

        return Array.from(typeCountMap.entries()).map(
            ([tileType, count]) => new TilePlacement(count, tileType)
        );
    }

    private reduceAvailableTilesFromBoard() {
        const container = document.getElementById('factories');
        if (!container) {
            return null;
        }

        const tilePlacements = this.extractTilePlacements(container);
        tilePlacements.forEach(tp => this.reduceAvailableTiles(tp));
    }

    private initializeLogObserve() {
        const logContainer = document.getElementById('logs');

        if (logContainer) {
            // Set flag to indicate we're processing initial log
            this.isProcessingInitialLog = true;
            // Start observing for changes immediately
            this.observeForLogEntry(logContainer);

            // Load existing log history and wait for DOM to be stable
            Promise.all([
                this.loadLog(false),
                waitForDomStable(200, logContainer)
            ]).then(() => {
                // Now process any changes that were queued during initial log processing
                this.processQueuedLogChanges();

                // Mark that initial processing is complete
                this.isProcessingInitialLog = false;
            });
        } else {
            // it should not happen because we wait for logs to be ready
            alert('hhhh');
            const observer = new MutationObserver(() => {
                const logsWrapNow = document.getElementById('logs');
                if (logsWrapNow) {
                    observer.disconnect();
                    this.observeForLogEntry(logsWrapNow);
                }
            });
            observer.observe(document.body, {childList: true, subtree: true});
        }
    }

    private processQueuedLogChanges() {
        console.log('DEBUG: Process queue log changes', this.logChangeQueue);

        let ignoreMoves = true;
        while (this.logChangeQueue.length > 0) {
            const element = this.logChangeQueue.shift();

            if (element.textContent && I18nTranslations.matches(element.textContent, 'Replay last moves') !== false) {
                ignoreMoves = false;
                continue;
            }

            if (element && !ignoreMoves) {
                console.log('DEBUG: analyze queue');
                this.analyzeLog(element);
            }
        }
    }

    private initializeAddOnStructure() {
        const archiveControl = document.getElementById('current_header_infos_wrap');
        if (!archiveControl) {
            console.warn('archivecontrol not found');
            return;
        }

        // create the container for tile counts
        const tileContainer = document.createElement('div');
        tileContainer.id = 'azul_tile_counter';
        tileContainer.className = 'azul-tile-counter';
        archiveControl.appendChild(tileContainer);

        // create the container for discarded tiles
        const discardedContainer = document.createElement('div');
        discardedContainer.id = 'azul_discarded_counter';
        discardedContainer.className = 'azul-tile-counter azul-discarded-counter';
        archiveControl.appendChild(discardedContainer);
    }

    observeForLogEntry(target: HTMLElement) {
        const observer = new MutationObserver((mutations) => {
            for (const mutation of mutations) {
                for (let i = 0; i < mutation.addedNodes.length; i++) {
                    const node = mutation.addedNodes[i];
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        const element = node as Element;

                        // Check if this is a log entry (roundedbox)
                        if (element.classList.contains('roundedbox')) {
                            if (this.isProcessingInitialLog) {
                                // Queue the change for later processing
                                this.logChangeQueue.push(element);
                            } else {
                                // Process immediately in real-time
                                this.analyzeLog(element);
                            }
                        }

                        // Also check for nested log entries
                        const nestedLogs = element.querySelectorAll('.roundedbox');
                        nestedLogs.forEach(logElement => {
                            if (this.isProcessingInitialLog) {
                                // Queue the change for later processing
                                this.logChangeQueue.push(logElement);
                            } else {
                                // Process immediately in real-time
                                this.analyzeLog(logElement);
                            }
                        });
                    }
                }
            }
        });

        observer.observe(target, {childList: true, subtree: true});
    }

    private reduceAvailableTiles(tilePlacement: TilePlacement) {
        const index = (tilePlacement.tileType + 2) % 5;
        const tile = this.availableTiles[index];
        if (tile) {
            tile.setCount(tile.count - tilePlacement.count);
        } else {
            console.warn('Tile not found for type:', tilePlacement.tileType);
        }
    }

    private newRoundBegins(): number {
        // iterate over all player boards
        for (const playerName in this.playersBoards) {
            const board = this.playersBoards[playerName];
            for (let i = 0; i < board.lines.length; i++) {
                const line = board.lines[i];
                const lineNumber = i + 1;
                if (line && line.count === lineNumber) {
                    // Add 1 tile to boardBag
                    this.boardBag.addTile(new TilePlacement(1, line.tileType));
                    // Add remaining tiles to discardedBag (if any)
                    if (lineNumber > 1) {
                        this.discardedBag.addTile(new TilePlacement(lineNumber - 1, line.tileType));
                        console.log('discarded bag');
                        console.log(this.discardedBag);
                    }
                    // Clear the line
                    board.lines[i] = null;
                }
            }
        }

        // check if we need to refill from discarded bag
        if (this.tilesInBagCount - this.factoriesCount * 4 < 0) {
            // get all tiles from discarded bag
            const discardedTiles = this.discardedBag.getTiles();

            // add them back to available tiles
            discardedTiles.forEach(tilePlacement => {
                if (tilePlacement) {
                    const index = (tilePlacement.tileType + 2) % 5;
                    const tile = this.availableTiles[index];
                    if (tile) {
                        tile.setCount(tile.count + tilePlacement.count);
                    }
                }
            });

            // increase tiles in bag by the number of discarded tiles
            const totalDiscarded = discardedTiles.reduce((sum, tp) => sum + (tp ? tp.count : 0), 0);

            // clear the discarded bag
            this.discardedBag.clear();

            return totalDiscarded;
        }

        return 0;
    }

    private placeTileOnLine(playerName: string, lineNumber: number, count: number, tileType: number) {
        // ignores first player tile for now
        if (tileType === 0) {
            return;
        }
        const tilePlacement = new TilePlacement(count, tileType);
        if (lineNumber > 0) {
            const discardedTiles = this.playersBoards[playerName].addTilesToLine(lineNumber, tilePlacement);
            this.lastMoves[playerName] = {lineNumber, tilePlacement: tilePlacement, discardedTiles};

            if (discardedTiles !== null) {
                this.discardedBag.addTile(discardedTiles);
            }

            return;
        }

        this.discardedBag.addTile(tilePlacement);

        if (playerName) {
            this.lastMoves[playerName] = {
                lineNumber: null,
                tilePlacement: tilePlacement,
                discardedTiles: tilePlacement
            };
        }
    }

    private undoSelectLine(playerName: string): void {
        if (this.lastMoves[playerName]) {
            const {lineNumber, tilePlacement, discardedTiles} = this.lastMoves[playerName];
            if (lineNumber !== null) {
                // remove the tiles from the line (reverse addTilesToLine)
                const board = this.playersBoards[playerName];
                const idx = lineNumber - 1;
                const line = board.lines[idx];
                if (line && line.tileType === tilePlacement.tileType) {
                    line.count -= tilePlacement.count;
                    if (line.count <= 0) {
                        board.lines[idx] = null;
                    }
                }
                // remove from discardedBag if it was added before
                if (discardedTiles) {
                    this.discardedBag.removeTile(discardedTiles);
                    console.log('undiscarded bag');
                }
            } else {
                // discard-only move: just remove from discardedBag
                if (discardedTiles) {
                    this.discardedBag.removeTile(discardedTiles);
                    console.log('undiscarded bag');
                }
            }

            // remove the last move record
            delete this.lastMoves[playerName];
        }
    }

    /**
     * Analyzes the content of logs_wrap to find tile placement actions.
     * @param container The HTMLElement to search within (e.g., logs_wrap)
     * @returns null if no "umieszcza" found, or an object with playerName, tilePlacements, and lineNumber
     */
    private analyzeLog(container: Element): {
        playerName: string,
        tilePlacements: TilePlacement[],
        lineNumber: number
    } | null {
        console.log(container.textContent);

        const parsed = this.parseLogEntry(container.textContent);

        if (parsed === null) {
            return null;
        }

        if (parsed.type === 'tile_placed_on_line') {
            const playerName = parsed.player_name ?? null;
            const tilePlacements = this.extractTilePlacements(container);
            const lineNumber = parsed.lineNumber ?? null;

            if (playerName && tilePlacements.length > 0 && lineNumber !== null) {
                this.placeTileOnLine(playerName, lineNumber, tilePlacements[0].count, tilePlacements[0].tileType);
            } else if (lineNumber === null) {
                console.log('should not be here, because tile placed on floor is another operation');
                this.placeTileOnLine(playerName, 0, tilePlacements[0].count, tilePlacements[0].tileType);
            }

            console.log(this.playersBoards[playerName].lines);

            return null;
        }

        if (parsed.type === 'tile_placed_on_floor') {
            const playerName = parsed.player_name ?? null;
            const tilePlacements = this.extractTilePlacements(container);

            this.placeTileOnLine(playerName, 0, tilePlacements[0].count, tilePlacements[0].tileType);

            return null;
        }


        if (parsed.type === 'new_round_starts') {
            const totalDiscarded = this.newRoundBegins();

            // decrease tiles in bag by factories × 4 + add tiles count from discarded bag if needed
            this.tilesInBagCount -= this.factoriesCount * 4 + totalDiscarded;
            if (this.stateLoaded === true) {
                const logs = document.getElementById('logs');
                const factories = document.getElementById('centered-table');
                Promise.all([
                    waitForDomStable(200, logs),
                    waitForDomStable(1000, factories),
                ]).then(() => {
                    this.reduceAvailableTilesFromBoard();
                });
            }

            return null;
        }

        if (parsed.type === 'cancels_tile_placement') {
            const playerName = parsed.player_name ?? null;

            if (playerName) {
                this.undoSelectLine(playerName);
            }

            console.log(this.playersBoards);
            return null;
        }

        /*
        if (container.textContent && container.textContent.includes(I18nTranslations.get('new_round_starts'))) {
            waitForDomStable(200).then(() => {
                this.reduceAvailableTilesFromBoard();
            });
        }*/

        return null;
    }

    private parseLogEntry(text: string) {
        for (let operationText in I18nTranslations.operationTypeByKey) {
            const parsed = I18nTranslations.matches(text, operationText);
            if (parsed !== false) {
                return parsed;
            }
        }

        return null;
    }

    waitForGameStable() {
        const logs = document.getElementById('logs');
        const factories = document.getElementById('centered-table');
        if (!logs || !factories) {
            console.warn('Logs or factories container not found!');
            return;
        }

        Promise.all([
            waitForDomStable(200, logs),
            waitForDomStable(1000, factories),
        ]).then(() => {
            this.onGameStable();
        });
    }

    onGameStable() {
        console.log('Game is now stable!');
        if (this.logsError) {
            this.reduceAvailableTilesFromBoard();
        }
        //this.boardBag.getTiles().forEach(tp => this.reduceAvailableTiles(tp));
        //this.discardedBag.getTiles().forEach(tp => this.reduceAvailableTiles(tp));

        /*for (const playerName in this.playersBoards) {
            const board = this.playersBoards[playerName];
            board.lines.forEach(tp => {
                if (tp !== null) {
                    this.reduceAvailableTiles(tp);
                }
            });
        }*/

        this.stateLoaded = true;
    }
}
