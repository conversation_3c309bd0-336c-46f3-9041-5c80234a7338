/**
 * Waits for DOMContentLoaded (if not already fired) and then
 * watches for any DOM mutations. Once there have been no
 * mutations for `stableTime` milliseconds, resolves the Promise.
 *
 * @param {number} stableTime - milliseconds of stability to wait for (default 1000)
 * @param target
 * @returns {Promise<void>}
 */
export function waitForDomStable(stableTime: number = 1000, target: Node = document): Promise<void> {
    return new Promise((resolve) => {
        // step 1: wait for initial DOMContentLoaded if target is document
        const whenLoaded = (target === document && document.readyState === 'loading')
            ? new Promise(r => document.addEventListener('DOMContentLoaded', r, {once: true}))
            : Promise.resolve();

        whenLoaded.then(() => {
            let timerId = null;
            const resetTimer = () => {
                if (timerId !== null) clearTimeout(timerId);
                timerId = setTimeout(() => {
                    observer.disconnect();
                    resolve(void 0);
                }, stableTime);
            };

            const observer = new MutationObserver(() => {
                resetTimer();
            });

            observer.observe(target, {
                childList: true,
                subtree: true,
                attributes: true,
                characterData: true
            });

            resetTimer();
        });
    });
}

/**
 * Waits until the given class is present on the target element.
 * Resolves immediately if the class is already present.
 */
export function waitForClass(target: Element, className: string): Promise<void> {
    return new Promise((resolve) => {
        // defensive: only resolve if className is non-empty and target is valid
        if (!className || !target) return;

        const hasClass = () => target.classList.contains(className);

        if (hasClass()) {
            resolve();
            return;
        }
        const observer = new MutationObserver(() => {
            if (hasClass()) {
                observer.disconnect();
                resolve();
            }
        });
        observer.observe(target, {attributes: true, attributeFilter: ['class']});
    });
}
