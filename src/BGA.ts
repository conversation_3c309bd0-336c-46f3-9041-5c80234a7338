/**
 * BGA (Board Game Arena) utility class for handling global object access
 * and interaction with the BGA platform's JavaScript environment.
 */
export class BGA {
    /**
     * Waits for a global object to become available in the BGA environment.
     * @param objectPath - Dot-separated path to the object (e.g., 'g_i18n', 'bgaConfig.requestToken')
     * @param timeout - Maximum time to wait in milliseconds (default: 10000)
     * @returns Promise that resolves when the object is available
     */
    static async waitForGlobalObject(objectPath: string, timeout: number = 10000): Promise<void> {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();
            
            const checkObject = () => {
                if (Date.now() - startTime > timeout) {
                    reject(new Error(`Timeout waiting for ${objectPath}`));
                    return;
                }
                
                if (typeof (window as any).wrappedJSObject !== 'undefined') {
                    const unsafeWindow = (window as any).wrappedJSObject;
                    const value = BGA.getNestedProperty(unsafeWindow, objectPath);
                    
                    if (value !== undefined && value !== null) {
                        resolve();
                        return;
                    }
                }
                setTimeout(checkObject, 100);
            };
            checkObject();
        });
    }

    /**
     * Gets a nested property from an object using a dot-separated path.
     * @param obj - The object to traverse
     * @param path - Dot-separated path to the property (e.g., 'config.user.name')
     * @returns The value at the specified path, or undefined if not found
     */
    static getNestedProperty(obj: any, path: string): any {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : undefined;
        }, obj);
    }

    /**
     * Gets a global object from the BGA environment.
     * @param objectPath - Dot-separated path to the object (e.g., 'g_i18n', 'bgaConfig.requestToken')
     * @returns The global object or null if not found
     */
    static getGlobalObject<T>(objectPath: string): T | null {
        if ((window as any).wrappedJSObject) {
            const value = BGA.getNestedProperty((window as any).wrappedJSObject, objectPath);
            return value || null;
        }

        console.warn(`Could not find ${objectPath} object`);
        return null;
    }

    /**
     * Waits for a global object to become available and then returns it.
     * This combines waitForGlobalObject and getGlobalObject into a single operation.
     * @param objectPath - Dot-separated path to the object (e.g., 'g_i18n', 'bgaConfig.requestToken')
     * @param timeout - Maximum time to wait in milliseconds (default: 10000)
     * @returns Promise that resolves with the object when it becomes available
     */
    static async waitForAndGetGlobalObject<T>(objectPath: string, timeout: number = 10000): Promise<T> {
        await BGA.waitForGlobalObject(objectPath, timeout);
        const obj = BGA.getGlobalObject<T>(objectPath);
        if (obj === null) {
            throw new Error(`Failed to get ${objectPath} after waiting`);
        }
        return obj;
    }
}
