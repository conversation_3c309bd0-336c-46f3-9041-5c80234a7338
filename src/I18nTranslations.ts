export interface I18nObject {
    nlsStrings: Record<string, Record<string, string>>;
    activeBundle: string;
    jsbundlesversion: string;
}

// https://boardgamearena.com/translation?module_id=1467&source_locale=en_US&dest_locale=pl_PL&findtype=all&find=places
export class I18nTranslations {
    public static translations: Record<string, string> = {};

    // Map canonical keys -> operation types (language independent)
    public static operationTypeByKey: Record<string, string> = {
        '${player_name} places ${number} ${color} on line ${lineNumber}': 'tile_placed_on_line',
        '${player_name} cancels tile placement': 'cancels_tile_placement',
        '${player_name} places ${number} ${color} on floor line': 'tile_placed_on_floor_line',
        'A new round begins !': 'new_round_starts',
        'Replay last moves': 'replay_last_moves',
    };

    private static defaultLanguage = 'en';

    // cache for getMatcher results
    private static matcherCache: Record<string, RegExp | string> = {};

    static addTranslation(key: string, translation: string): void
    {
        this.translations[key] = translation;
    }

    /** @deprecated */
    static getLanguage(): string {
        // Read lang attribute and normalize to short code (en, pl, ...)
        const htmlElement = document.documentElement;
        const rawLang = htmlElement.getAttribute('lang') ||
            htmlElement.getAttribute('xml:lang') ||
            htmlElement.getAttribute('xmlns:lang') ||
            (navigator.language || '');
        const langCode = rawLang ? rawLang.split(/[-_]/)[0] : this.defaultLanguage;
        return this.translations[langCode] ? langCode : this.defaultLanguage;
    }

    static get(key: string): string {
        return this.translations[key] || key;
    }

    /**
     * Check if a key contains placeholders that require regex matching
     */
    static hasPlaceholders(key: string): boolean {
        return /\$\{[^}]+\}/.test(key);
    }

    /**
     * Get the appropriate matcher for a key - either regex or direct string
     */
    static getMatcher(key: string): RegExp | string {
        if (this.matcherCache[key]) {
            return this.matcherCache[key];
        }

        let result: RegExp | string;
        if (this.hasPlaceholders(key)) {
            result = this.getRegex(key);
        } else {
            result = this.get(key);
        }

        this.matcherCache[key] = result;

        return result;
    }

    /**
     * Test if text matches a key, automatically choosing between regex and direct comparison
     * Returns parsed match object for regex matches or direct string matches, false if not found
     */
    static matches(text: string, key: string): {
        type: string,
        player_name?: string,
        number?: number,
        color?: string | number,
        lineNumber?: number
    } | false {
        const matcher = this.getMatcher(key);

        if (matcher instanceof RegExp) {
            // Remove HTML comments from the text for regex matching
            const cleanText = text.replace(/<!--[\s\S]*?-->/g, '');
            const match = cleanText.match(matcher);

            if (match) {
                const parsedMatch = this.parseMatch(match, key);
                return parsedMatch || false;
            } else {
                return false;
            }
        } else {
            // direct string comparison for keys without placeholders
            if (!text.includes(matcher)) {
                return false;
            }

            return {type: this.operationTypeByKey[matcher]}
        }
    }

    /**
     * Build a regex for the given canonical key using the current language's translation.
     * Replaces placeholders with HTML-aware capture groups:
     *  - ${player_name} -> an element with class "playername"
     *  - ${number} -> one or more tile divs (we capture the sequence)
     *  - ${color} -> either a tile class (e.g. tile3) or a plain word
     *  - ${lineNumber} -> a <strong>digit</strong> or plain digit
     */
    static getRegex(key: string): RegExp {
        const template = this.translations[key] || key;

        // Remove HTML comments from the template
        let tmp = template.replace(/<!--[\s\S]*?-->/g, '');

        // Replace placeholders with markers
        tmp = tmp
            .replace(/\$\{player_name\}/g, '___PLAYER_NAME___')
            .replace(/\$\{number\}/g, '___NUMBER___')
            .replace(/\$\{color\}/g, '') // remove color
            .replace(/\$\{lineNumber\}/g, '___LINENUM___');

        // Trim extra spaces and normalize whitespace
        tmp = tmp.trim().replace(/\s+/g, ' ');

        // Escape regex-special characters
        tmp = tmp.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

        // Replace markers with HTML-aware capture groups
        tmp = tmp.replace(
            /___PLAYER_NAME___/g,
            '(<[^>]*class=["\']playername["\'][^>]*>[\\s\\S]*?<\\/[^>]*>)'
        );

        tmp = tmp.replace(
            /___NUMBER___/g,
            '((?:<div[^>]*class=["\'][^"\']*tile(\\d+)[^"\']*["\'][^>]*>\\s*<\\/div>\\s*)+)'
        );

        tmp = tmp.replace(
            /___LINENUM___/g,
            '(?:<strong>\\s*([0-9]+)\\s*<\\/strong>|([0-9]+))'
        );

        // Clean up extra spaces and make the pattern more flexible
        tmp = tmp.replace(/\\s{2,}/g, '\\s*');

        // Allow arbitrary surrounding content
        return new RegExp('[\\s\\S]*' + tmp + '[\\s\\S]*', 'i');
    }

    /**
     * Try to find the canonical key whose translation matches the provided text.
     * Returns the key or null.
     */
    static findMatchingKey(text: string): string | null {
        // remove HTML comments from the text for matching
        const cleanText = text.replace(/<!--[\s\S]*?-->/g, '');

        for (const key in this.translations) {
            const regex = this.getRegex(key);
            if (regex.test(cleanText)) {
                return key;
            }
        }

        return null;
    }

    static has(key: string): boolean {
        return key in this.translations;
    }

    /**
     * Parse the RegExp match into a structured object.
     * Note: this relies on the same placeholder order that getRegex uses.
     */
    static parseMatch(match: RegExpMatchArray, key: string): {
        type: string,
        player_name?: string,
        number?: number,
        color?: string | number,
        lineNumber?: number
    } {
        // Find placeholder order from the canonical key's translation (placeholders in that string)
        const placeholders: string[] = [];
        const placeholderRegex = /\$\{([^}]+)\}/g;
        let placeholderMatch: string[];
        while ((placeholderMatch = placeholderRegex.exec(key)) !== null) {
            placeholders.push(placeholderMatch[1]);
        }

        const result: any = {};

        // Operation type is mapped from the canonical key (language-independent)
        result.type = this.operationTypeByKey[key] || 'unknown';

        // The getRegex created multiple capture groups for some placeholders; we need to step through match groups.
        // We'll iterate through placeholders and attempt to locate the correct capture groups manually.
        // Because some placeholders expand into multiple capturing groups (color, lineNumber), we carefully attempt to extract.
        let groupIndex = 1; // match[0] is full match

        for (const placeholder of placeholders) {
            if (groupIndex >= match.length) break;

            switch (placeholder) {
                case 'player_name': {
                    const playerHtml = match[groupIndex] || '';
                    // extract inner text of .playername element
                    const playerNameMatch = playerHtml.match(/<[^>]*class=["']playername["'][^>]*>([^<]*)</i);
                    result.player_name = playerNameMatch ? playerNameMatch[1].trim() : playerHtml;
                    groupIndex += 1;
                    break;
                }
                case 'number': {
                    const tilesHtml = match[groupIndex] || '';
                    const tileDivs = tilesHtml.match(/<div[^>]*class=["'][^"']*tile[^"']*["'][^>]*>/g);
                    result.number = tileDivs ? tileDivs.length : 0;

                    // extract color from the first tile class
                    if (tileDivs && tileDivs.length > 0) {
                        const firstTile = tileDivs[0];
                        const colorMatch = firstTile.match(/tile(\d+)/);
                        if (colorMatch) {
                            result.color = parseInt(colorMatch[1], 10);
                        }
                    }

                    groupIndex += 1;
                    break;
                }
                case 'color': {
                    // Color is now extracted from tile classes in the 'number' case
                    // This case is kept for backward compatibility but should not be reached
                    groupIndex += 1;
                    break;
                }
                case 'lineNumber': {
                    // Our lineNumber pattern gave two places: one subcapture with <strong>digits</strong> or a fallback capture for digits.
                    // Try to find a numeric capture in the next few groups.
                    let foundLine: number | undefined;
                    for (let gi = groupIndex; gi < Math.min(match.length, groupIndex + 3); gi++) {
                        const val = match[gi];
                        if (!val) continue;
                        const digitsMatch = val.match(/([0-9]+)/);
                        if (digitsMatch) {
                            foundLine = parseInt(digitsMatch[1], 10);
                            break;
                        }
                    }
                    result.lineNumber = foundLine !== undefined ? foundLine : undefined;
                    groupIndex += 3;
                    break;
                }
                default:
                    groupIndex += 1;
            }
        }

        return result;
    }

    /**
     * Convenience: find matching translation key (if not provided) and parse the input text.
     * Returns parsed object or null if nothing matched.
     */
    static matchAndParse(text: string, key?: string): {
        type: string,
        player_name?: string,
        number?: number,
        color?: string | number,
        lineNumber?: number
    } | null {
        const matchingKey = key || this.findMatchingKey(text);
        if (!matchingKey) {
            return null;
        }

        const regex = this.getRegex(matchingKey);
        // remove HTML comments from the text for matching
        const cleanText = text.replace(/<!--[\s\S]*?-->/g, '');
        const match = cleanText.match(regex);

        if (match) {
            return this.parseMatch(match, matchingKey);
        }

        return null;
    }
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = {I18nTranslations};
}
